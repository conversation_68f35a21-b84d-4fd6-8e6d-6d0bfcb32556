import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:image_network/image_network.dart';
import 'package:get/get.dart';
import 'package:money_mouthy_two/controllers/profile_controller.dart';
import 'package:money_mouthy_two/screens/connect_screen.dart';
import '../services/post_service.dart';
import '../services/wallet_service.dart';
import '../widgets/post_card.dart';
import 'post_detail_screen.dart';

class ProfileScreen extends StatelessWidget {
  final String userId;
  const ProfileScreen({super.key, required this.userId});

  Future<Map<String, dynamic>?> _loadUser() async {
    final doc =
        await FirebaseFirestore.instance.collection('users').doc(userId).get();
    return doc.data();
  }

  Widget _buildProfileContent(
    String username,
    String bio,
    String? profileImageUrl,
    String? coverImageUrl,
    bool isSelf,
    PostService postService,
    WalletService walletService,
    BuildContext context,
  ) {
    final userPosts = postService
        .getAllPosts()
        .where((p) => p.authorId == userId)
        .toList();

    return SingleChildScrollView(
      child: Column(
        children: [
          const SizedBox(height: 20),
          // Profile Image
          Center(
            child: CircleAvatar(
              radius: 60,
              backgroundColor: Colors.grey.shade300,
              child: ClipOval(
                child: ImageNetwork(
                  image: profileImageUrl ?? '',
                  height: 120,
                  width: 120,
                  duration: 500,
                  curve: Curves.easeIn,
                  onPointer: true,
                  debugPrint: false,
                  fullScreen: false,
                  fitAndroidIos: BoxFit.cover,
                  fitWeb: BoxFitWeb.cover,
                  onLoading: const CircularProgressIndicator(
                    color: Colors.indigoAccent,
                  ),
                  onError: Text(
                    username.split(' ').map((e) => e[0]).take(2).join(),
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 24,
                    ),
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(height: 16),
          // Username
          Text(
            '@$username',
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          // Bio
          if (bio.isNotEmpty)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                bio,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey[600],
                ),
              ),
            ),
          const SizedBox(height: 20),
          // Posts count and other stats can be added here
          Text(
            '${userPosts.length} Posts',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 20),
          // Posts grid or list can be added here
          if (userPosts.isNotEmpty)
            ...userPosts.map((post) => Container(
              margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Text('Post: ${post.content}'),
            )).toList()
          else
            const Padding(
              padding: EdgeInsets.all(20),
              child: Text(
                'No posts yet',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey,
                ),
              ),
            ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final currentUid = FirebaseAuth.instance.currentUser?.uid;
    final bool isSelf = currentUid == userId;
    final ProfileController profileController = Get.find<ProfileController>();

    final PostService postService = PostService();
    final WalletService walletService = WalletService();

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        automaticallyImplyLeading: false,
        titleSpacing: 0,
        centerTitle: true,
        title: Row(
          children: [
            const Spacer(),
            // Paid Post label – show if user has paid posts
            FutureBuilder<List<Post>>(
              future: Future.value(
                postService
                    .getAllPosts()
                    .where((p) => p.authorId == userId && p.price > 0)
                    .toList(),
              ),
              builder: (context, postSnap) {
                if (!postSnap.hasData || postSnap.data!.isEmpty)
                  return const SizedBox();
                final topPrice = postSnap.data!
                    .map((e) => e.price)
                    .reduce((a, b) => a > b ? a : b);
                return Row(
                  children: [
                    Text(
                      'Paid Post',
                      style: TextStyle(
                        color: Colors.black,
                        fontWeight: FontWeight.w600,
                        decoration: TextDecoration.underline,
                        decorationThickness: 1,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade100,
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(color: Colors.grey.shade400),
                      ),
                      child: Text(
                        '\$ ${topPrice.toStringAsFixed(0)}',
                        style: const TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),
            const SizedBox(width: 16),
          ],
        ),
        actions:
            isSelf
                ? [
                  IconButton(
                    icon: const Icon(Icons.edit, color: Colors.black),
                    onPressed: () {
                      Navigator.pushNamed(context, '/edit_profile');
                    },
                  ),
                ]
                : null,
      ),
      body: Center(
        child: Container(
          constraints: BoxConstraints(maxWidth: kIsWeb ? 800 : double.infinity),
          child: isSelf
              ? Obx(() => _buildProfileContent(
                  profileController.username.isNotEmpty ? profileController.username : 'Unknown',
                  profileController.bio,
                  profileController.profileImageUrl,
                  null, // coverImageUrl not used
                  isSelf,
                  postService,
                  walletService,
                  context,
                ))
              : FutureBuilder<Map<String, dynamic>?>(
                  future: _loadUser(),
                  builder: (context, snap) {
                    if (snap.connectionState == ConnectionState.waiting) {
                      return const Center(child: CircularProgressIndicator());
                    }
                    if (!snap.hasData || snap.data == null) {
                      return const Center(child: Text('No Data Found'));
                    }
                    final data = snap.data!;
                    final username = data['username'] ?? 'Unknown';
                    final bio = data['bio'] ?? '';
                    final profileImageUrl = data['profileImageUrl'];

                    return _buildProfileContent(
                      username,
                      bio,
                      profileImageUrl,
                      null, // coverImageUrl not used
                      isSelf,
                      postService,
                      walletService,
                      context,
                    );
                  },
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header with cover image and profile avatar
                    // Profile image positioned at top
                    Align(
                      alignment: Alignment.centerLeft,
                      child: Padding(
                        padding: const EdgeInsets.all(0),
                        child: Stack(
                          alignment: Alignment.bottomLeft,
                          children: [
                            Center(
                              child: CircleAvatar(
                                radius: 60,
                                backgroundColor: Colors.grey.shade300,
                                child: ClipOval(
                                  child: ImageNetwork(
                                    image: profileImageUrl ?? '',
                                    height: 120,
                                    width: 120,
                                    duration: 500,
                                    curve: Curves.easeIn,
                                    onPointer: true,
                                    debugPrint: false,
                                    backgroundColor: Colors.white,
                                    fitAndroidIos: BoxFit.cover,
                                    fitWeb: BoxFitWeb.cover,
                                    borderRadius: BorderRadius.circular(70),
                                    onLoading: CircularProgressIndicator(
                                      color: Colors.indigoAccent,
                                      strokeWidth: 0.1,
                                    ),
                                    onError: const Icon(
                                      Icons.person,
                                      color: Colors.blue,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            // if (isSelf)
                            //   Positioned(
                            //     right: -4,
                            //     bottom: -4,
                            //     child: Container(
                            //       decoration: const BoxDecoration(
                            //         color: Color(0xFF5159FF),
                            //         shape: BoxShape.circle,
                            //       ),
                            //       padding: const EdgeInsets.all(4),
                            //       child: const Icon(
                            //         Icons.edit,
                            //         size: 14,
                            //         color: Colors.white,
                            //       ),
                            //     ),
                            //   ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Text(
                        username,
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    if (bio.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 4,
                        ),
                        child: Text(
                          bio,
                          style: const TextStyle(color: Colors.grey),
                        ),
                      ),
                    const SizedBox(height: 16),

                    // Fund Account Section (self only)
                    if (isSelf) ...[
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: Text(
                          'Fund Account',
                          style: TextStyle(
                            color: Colors.grey[800],
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: _AddPaymentButton(
                          onPressed: () {
                            Navigator.pushNamed(context, '/wallet');
                          },
                        ),
                      ),
                      const SizedBox(height: 16),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: ValueListenableBuilder<double>(
                          valueListenable: ValueNotifier<double>(
                            walletService.currentBalance,
                          ),
                          builder: (context, balance, _) {
                            final pct = (balance / 100).clamp(0.0, 1.0);
                            return Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Stack(
                                  children: [
                                    Container(
                                      height: 2,
                                      decoration: BoxDecoration(
                                        color: Colors.grey.shade400,
                                      ),
                                    ),
                                    FractionallySizedBox(
                                      widthFactor: pct,
                                      child: Container(
                                        height: 2,
                                        color: const Color(0xFF5159FF),
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 4),
                                Text(walletService.formatCurrency(balance)),
                              ],
                            );
                          },
                        ),
                      ),
                      const SizedBox(height: 24),
                    ],

                    // Follow other accounts section (only for self)
                    if (isSelf) ...[
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: const [
                                Text(
                                  'Follow Other accounts',
                                  style: TextStyle(fontWeight: FontWeight.w600),
                                ),
                                SizedBox(height: 2),
                                Text(
                                  'Connect with other users around the world.',
                                  style: TextStyle(
                                    color: Colors.grey,
                                    fontSize: 12,
                                  ),
                                ),
                              ],
                            ),
                            IconButton(
                              icon: const Icon(Icons.close),
                              onPressed: () {},
                            ),
                          ],
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 12,
                        ),
                        child: ElevatedButton.icon(
                          onPressed: () {
                            Navigator.of(context).pushNamed('/connect');
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF5159FF),
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            minimumSize: const Size(double.infinity, 48),
                          ),
                          icon: const Icon(Icons.people),
                          label: const Text('Connect'),
                        ),
                      ),
                      const SizedBox(height: 16),
                    ],

                    // User posts list
                    // Padding(
                    //   padding: const EdgeInsets.symmetric(
                    //     horizontal: 16,
                    //     vertical: 8,
                    //   ),
                    //   child: Text(
                    //     'Posts',
                    //     style: TextStyle(
                    //       color: Colors.grey[800],
                    //       fontWeight: FontWeight.w600,
                    //     ),
                    //   ),
                    // ),
                    // if (userPosts.isEmpty)
                    //   const Center(
                    //     child: Padding(
                    //       padding: EdgeInsets.all(24.0),
                    //       child: Text('This user has no posts'),
                    //     ),
                    //   )
                    // else
                    //   ListView.builder(
                    //     physics: const NeverScrollableScrollPhysics(),
                    //     shrinkWrap: true,
                    //     itemCount: userPosts.length,
                    //     itemBuilder: (context, i) {
                    //       final post = userPosts[i];
                    //       return PostCard(
                    //         post: post,
                    //         isDetailView: false,
                    //         showActions: false,
                    //         onLike: () => {},
                    //         onPurchase: () => {},
                    //         onView: () => {},
                    //         onTap:
                    //             () => {
                    //               Navigator.push(
                    //                 context,
                    //                 MaterialPageRoute(
                    //                   builder:
                    //                       (context) =>
                    //                           PostDetailScreen(post: post),
                    //                 ),
                    //               ),
                    //             },
                    //       );
                    //     },
                    //   ),
                    // const SizedBox(height: 32),
                  ],
                ),
              ),
        ),
      ),
    );
  }
}

class _AddPaymentButton extends StatelessWidget {
  final VoidCallback onPressed;
  const _AddPaymentButton({required this.onPressed});

  @override
  Widget build(BuildContext context) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: const Color(0xFF5159FF),
        foregroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(24)),
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 24),
      ),
      icon: const Icon(Icons.add),
      label: const Text('ReUp'),
    );
  }
}
