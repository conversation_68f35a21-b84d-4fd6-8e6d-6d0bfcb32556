import 'package:get/get.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../services/category_preference_service.dart';
import '../widgets/home/<USER>';

/// GetX Controller for managing category state across the entire app
class CategoryController extends GetxController {
  static CategoryController get instance => Get.find();

  // Reactive variables
  final _selectedCategoryIndex = 0.obs;
  final _selectedCategoryName = 'News'.obs;
  final _isLoading = false.obs;

  // Getters for reactive access
  int get selectedCategoryIndex => _selectedCategoryIndex.value;
  String get selectedCategoryName => _selectedCategoryName.value;
  bool get isLoading => _isLoading.value;

  // Reactive getters for UI binding
  RxInt get selectedCategoryIndexRx => _selectedCategoryIndex;
  RxString get selectedCategoryNameRx => _selectedCategoryName;
  RxBool get isLoadingRx => _isLoading;

  // Get current category data
  CategoryData get selectedCategory => Categories.all[selectedCategoryIndex];

  @override
  void onInit() {
    super.onInit();
    _loadUserCategory();
  }

  /// Load user's selected category from Firebase
  Future<void> _loadUserCategory() async {
    try {
      _isLoading.value = true;
      
      final user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        final userDoc = await FirebaseFirestore.instance
            .collection('users')
            .doc(user.uid)
            .get();

        if (userDoc.exists) {
          final userData = userDoc.data();
          final savedCategory = userData?['selectedCategory'];

          if (savedCategory != null) {
            final categoryIndex = Categories.all.indexWhere(
              (cat) => cat.name == savedCategory,
            );

            if (categoryIndex != -1) {
              _selectedCategoryIndex.value = categoryIndex;
              _selectedCategoryName.value = savedCategory;
              debugPrint(
                'CategoryController: Loaded saved category "$savedCategory" at index $categoryIndex',
              );
            }
          }
        }
      }
    } catch (e) {
      debugPrint('CategoryController: Error loading user category: $e');
      // Fallback to default category
      _selectedCategoryIndex.value = 0;
      _selectedCategoryName.value = Categories.all[0].name;
    } finally {
      _isLoading.value = false;
    }
  }

  /// Update selected category and sync to Firebase
  Future<void> updateCategory(int categoryIndex) async {
    if (categoryIndex < 0 || categoryIndex >= Categories.all.length) {
      debugPrint('CategoryController: Invalid category index: $categoryIndex');
      return;
    }

    if (categoryIndex == _selectedCategoryIndex.value) {
      debugPrint('CategoryController: Category already selected');
      return;
    }

    try {
      _isLoading.value = true;
      
      final categoryName = Categories.all[categoryIndex].name;
      
      // Update reactive variables immediately for UI responsiveness
      _selectedCategoryIndex.value = categoryIndex;
      _selectedCategoryName.value = categoryName;

      debugPrint(
        'CategoryController: Updated category to "$categoryName" at index $categoryIndex',
      );

      // Save to Firebase and SharedPreferences
      await _saveCategoryToFirebase(categoryName);
      await CategoryPreferenceService.saveLastSelectedCategory(categoryName);

    } catch (e) {
      debugPrint('CategoryController: Error updating category: $e');
      // Revert on error
      await _loadUserCategory();
    } finally {
      _isLoading.value = false;
    }
  }

  /// Update category by name
  Future<void> updateCategoryByName(String categoryName) async {
    final categoryIndex = Categories.all.indexWhere(
      (cat) => cat.name == categoryName,
    );

    if (categoryIndex != -1) {
      await updateCategory(categoryIndex);
    } else {
      debugPrint('CategoryController: Category "$categoryName" not found');
    }
  }

  /// Save category to Firebase
  Future<void> _saveCategoryToFirebase(String categoryName) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        await FirebaseFirestore.instance
            .collection('users')
            .doc(user.uid)
            .update({
          'selectedCategory': categoryName,
          'updatedAt': FieldValue.serverTimestamp(),
        });
        
        debugPrint('CategoryController: Saved category "$categoryName" to Firebase');
      }
    } catch (e) {
      debugPrint('CategoryController: Error saving category to Firebase: $e');
      rethrow;
    }
  }

  /// Refresh category from Firebase (useful for syncing across devices)
  Future<void> refreshCategory() async {
    await _loadUserCategory();
  }

  /// Get category color by index
  Color getCategoryColor(int index) {
    if (index >= 0 && index < Categories.all.length) {
      return Categories.all[index].color;
    }
    return Categories.all[0].color; // Default to first category color
  }

  /// Get category name by index
  String getCategoryName(int index) {
    if (index >= 0 && index < Categories.all.length) {
      return Categories.all[index].name;
    }
    return Categories.all[0].name; // Default to first category name
  }

  /// Check if a category is currently selected
  bool isCategorySelected(int index) {
    return index == _selectedCategoryIndex.value;
  }

  /// Get all available categories
  List<CategoryData> get allCategories => Categories.all;

  /// Reset to default category (News)
  Future<void> resetToDefault() async {
    await updateCategory(0); // News is at index 0
  }
}
